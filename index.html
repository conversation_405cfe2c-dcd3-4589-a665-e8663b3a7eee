<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta charset="utf-8" />
    <title>Navigation Buttons</title>
    <link rel="stylesheet" href="globals.css" />
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <nav class="button-on-dark" role="navigation" aria-label="主导航">
      <button class="button-on-light" type="button" aria-pressed="true" aria-label="首页">
        <span class="label">􀤑 首页</span>
      </button>
      <button class="text-wrapper" type="button" aria-pressed="false" aria-label="主持">􀤑 主持</button>
    </nav>
  </body>
</html>

@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}

.button-on-dark {
  display: flex;
  width: 208px;
  align-items: center;
  gap: 15px;
  padding: 5px 21px 5px 5px;
  position: relative;
  border-radius: 100px;
  overflow: hidden;
  background: linear-gradient(
      0deg,
      rgba(230, 230, 230, 1) 0%,
      rgba(230, 230, 230, 1) 100%
    ),
    linear-gradient(0deg, rgba(51, 51, 51, 0.3) 0%, rgba(51, 51, 51, 0.3) 100%);
}

.button-on-dark .button-on-light {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 14px 21px 14px 20px;
  position: relative;
  flex: 0 0 auto;
  border-radius: 100px;
  overflow: hidden;
  box-shadow:
    0px 1px 8px #0000001a, 0px 0px 2px #0000001a, inset 0px 0px 8px #f2f2f2, inset 0px 0px 0px 1px #a6a6a6, inset -2px -2px 0.5px -2px #262626, inset 2px 2px 0.5px -2px #262626, inset 3px 3px 0.5px -3.5px #ffffff;
  backdrop-filter: blur(6px) brightness(100%);
  -webkit-backdrop-filter: blur(6px) brightness(100%);
  background: linear-gradient(
      0deg,
      rgba(23, 23, 23, 1) 0%,
      rgba(23, 23, 23, 1) 100%
    ),
    linear-gradient(
      0deg,
      rgba(140, 140, 140, 0.25) 0%,
      rgba(140, 140, 140, 0.25) 100%
    );
}

.button-on-dark .label {
  position: relative;
  width: fit-content;
  font-family: "SF Pro-Semibold", Helvetica;
  font-weight: 400;
  color: #ffffff;
  font-size: 17px;
  letter-spacing: -0.10px;
  line-height: 20px;
  white-space: nowrap;
}

.button-on-dark .text-wrapper {
  position: relative;
  width: fit-content;
  font-family: "SF Pro-Semibold", Helvetica;
  font-weight: 400;
  color: #ffffff80;
  font-size: 17px;
  letter-spacing: -0.10px;
  line-height: 20px;
  white-space: nowrap;
}
/* Original CSS code should be injected here */

/* Enhanced accessibility and interaction styles */
.button-on-dark .button-on-light:focus-visible,
.button-on-dark .text-wrapper:focus-visible {
  outline: 2px solid #4a90e2;
  outline-offset: 2px;
}

.button-on-dark .button-on-light:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

.button-on-dark .text-wrapper:hover {
  color: #ffffff;
  transition: color 0.2s ease;
}

.button-on-dark .button-on-light:active {
  transform: translateY(0);
}

.button-on-dark .text-wrapper {
  background: none;
  border: none;
  cursor: pointer;
  padding: 14px 0;
}

.button-on-dark .button-on-light {
  background: linear-gradient(
      0deg,
      rgba(23, 23, 23, 1) 0%,
      rgba(23, 23, 23, 1) 100%
    ),
    linear-gradient(
      0deg,
      rgba(140, 140, 140, 0.25) 0%,
      rgba(140, 140, 140, 0.25) 100%
    );
  border: none;
  cursor: pointer;
}

.button-on-dark .button-on-light[aria-pressed="true"] {
  box-shadow:
    0px 1px 8px #0000001a, 0px 0px 2px #0000001a, inset 0px 0px 8px #f2f2f2, inset 0px 0px 0px 1px #a6a6a6, inset -2px -2px 0.5px -2px #262626, inset 2px 2px 0.5px -2px #262626, inset 3px 3px 0.5px -3.5px #ffffff;
}

.button-on-dark .text-wrapper[aria-pressed="false"] {
  color: #ffffff80;
}

@media (prefers-reduced-motion: reduce) {
  .button-on-dark .button-on-light:hover,
  .button-on-dark .text-wrapper:hover {
    transition: none;
  }
}

